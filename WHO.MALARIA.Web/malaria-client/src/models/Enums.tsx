export enum DialogAction {
    None = 0,
    Add = 1,
    Edit = 2,
    Delete = 3,
    Close = 4,
}

export enum SnackbarType {
    None = -1,
    Default = 0,
    Success = 1,
    Error = 2,
    Information = 3,
}

export enum StatusCode {
    Ok = 200,
    Created = 201,
    UnAuthorized = 401,
    NotFound = 404,
    PreConditionFailed = 412,
    InternalServerError = 500,
    NoContent = 204,
}

export enum ValidationStatus {
    Failed = 100,
    Success = 200,
}

// authentication scheme
export enum Scheme {
    Google = "Google",
    Facebook = "Facebook",
    Azure = "Azure",
}

export enum AssessmentStrategies {
    Case = 1,
    MalariaControl = 2,
}

export enum AssessmentApproach {
    Rapid = 1,
    Tailored = 2,
    Comphrehensive = 3,
}

/// <summary>
/// Enum used to define a preference  
/// </summary>
export enum Preference {
    Priority = 1,
    Optional = 2,
}

export enum StrategyStepper {
    Strategy = 0,
    Indicator = 1,
}

export enum AssessmentTabs {
    ScopeDefinition = 0,
    DataCollection = 1,
    DataAnalysis = 2,
    ReportGeneration = 3,
}

/// <summary>
/// Data collection steppers enum
/// </summary>
export enum DataCollectionStepper {
    DeskReview = 0,
    DQA = 1,
    QuestionBank = 2,
    SurveyResult = 3
}

/// <summary>
/// Report generation steppers enum
/// </summary>
export enum ReportGenerationStepper {
    ScoreCard = 0,
    Report = 1
}

/// <summary>
/// Data Quality Assessment steppers enum
/// </summary>
export enum DQASteps {
    DeskLevelDelivery = 0,
    ServiceLevelDelivery = 1,
    EliminationDQA = 2,
}

/// <summary>
/// Represents register type of DQA
/// </summary>
export enum DQASLRegisterType {
    InpatientRegister = 1,
    OutpatientRegister = 2,
    LabRegister = 3
}

/// <summary>
/// Represents data system type for service level DQA
/// </summary>
export enum DQADataSystem {
    System1 = 1,
    System2 = 2
}

/// <summary>
/// User Status within at application level
/// </summary>
export enum UserStatus {
    /// <summary>
    /// If user has been marked in active by SuperManager and Manager or WHO
    /// </summary>
    InActive = 0,

    /// <summary>
    /// When user accepts the invitation and log in for the first time , the status changes from InvitationSent to Active
    /// </summary>
    Active = 1,

    /// <summary>
    /// When an invitation is sent to User
    /// </summary>
    InvitationSent = 2,

    /// <summary>
    /// When user regisger themselves, the default status would 'Pending'
    /// </summary>
    Pending = 3,
}

/// <summary>
/// Represents user country acess rights status
/// </summary>
export enum UserCountryAccessRightsEnum {
    Pending = 0,
    Accepted = 1,
    Rejected = 2,
    InActive = 3,
    InvitationNotAccepted = 4
}

export enum UserKind {
    WHO = "WHO",
    NonWHO = "NonWHO",
}

/// <summary>
/// Represent user role which is basically UserType
/// </summary>
export enum UserRoleEnum {
    WhoViewer = 0,
    Viewer = 1,
    Manager = 2,
    SuperManager = 3,
    WHOAdmin = 4
}

/// <summary>
/// Represent assessment user role
/// </summary>
export enum AssessmentUserRole {
    Manager = 1,
    Editor = 2,
    Reviewer = 3
}

/// <summary>
/// Represents status of assessment as assessment progresses
/// </summary>
export enum AssessmentStatus {
    /// <summary>
    /// Assessment is created
    /// </summary>
    Created = 1,

    /// <summary>
    /// Srategies are selected
    /// </summary>
    StrategySelected = 2,

    /// <summary>
    /// Assessment type and indicators are selected
    /// </summary>
    TypeSelected = 3,

    /// <summary>
    /// Scope definition is completed and finalized and the assessment is ready
    /// for data collection
    /// </summary>
    Finalized = 4,

    /// <summary>
    /// Data collection is in progress
    /// </summary>
    InProgress = 5,

    // <summary>
    /// Assessment is completed with data collection
    /// and published
    /// </summary>
    Published = 6,
}

/** Operators used for filter criteria */
export enum Operator {
    Equals = "=",
    NotEquals = "<>",
    GreaterThan = ">",
    LessThan = "<",
    GreaterThanEqualsTo = ">=",
    LessThanEqualsTo = "<=",
}

/** Enum for sorting data using field direction i.e Ascending or Descending*/
export enum SortDirection {
    //Value indicating the sorting order as Ascending
    Asc = 1,
    //Value indicating the sorting order as Descending
    Dsc = 2,
}

/** Enum for desk review status*/
export enum DeskReviewAssessmentResponseStatus {
    InProgress = 1,
    Completed = 2,
}

/** List of all master strategies for which response screen needs to be rendered */
export enum StrategiesEnum {
    BurdenReduction = "014BAA5E-9BE4-4FBE-AA23-B54509F49797",
    Elimination = "02F4B402-9706-44B9-A7F5-00284F04326A",
    Both = "21B7E764-7E3F-4FB4-9791-C22B7C1D2A11",
    Commodities = "126E3CFF-DA45-4BDE-AE01-0C09CAD1F895",
    LarvalSourceManagement = "11D72823-78C7-4D4B-B58E-1731CF295085",
    ITNsMassCampaign = "09C23080-D7FB-4551-9D79-1FB1DBEE8A33",
    IPTi = "0573123B-F7B6-4FEE-9EC3-23BA3ED1E8E1",
    SMC = "06D41C95-EDE5-4356-AD86-4F9ED881CC86",
    Ento = "134C6AF9-2F78-4331-AE87-5AF33FE25B55",
    Genomics = "158C12A0-92F3-47AD-A9B7-690D9F06846A",
    DrugEfficacy = "1476E1E1-0040-4137-9C0D-9E8542C1C6EA",
    ITNsRoutine = "08F45A34-F3A9-483F-AC7A-B684FF25BF51",
    IPTp = "04184CFB-0625-497F-993A-BBAA9D546FAA",
    MDA = "078A5DE3-CCE3-4B27-86B0-DAD8F9408DBD",
    IRS = "105083AD-0E17-4C58-B4B3-F0F67318C58A",
}

/** List of status for met and not met */
export enum MetNotMetEnum {
    Met = "Met",
    PartiallyMet = "PartiallyMet",
    NotMet = "NotMet",
}

//**Enum for type of Days */
export enum MonitoringFrequency {
    Daily = "Daily",
    Weekly = "Weekly",
    Monthly = "Monthly",
    Yearly = "Yearly",
    Quarterly = "Quarterly",
    Annually = "Annually",
    Biannually = "Biannually"
}

//**Enum for Name of strategy */
export enum StrategyName {
    Burden = "Burden",
    Elimination = "Elimination",
}

/**Contains primitive as well custom data types */
export enum DataType {
    String = "string",
    ArrayOfKeyValuePair = "array-of-key-value-pair",
    Object = "object",
    Number = "number",
    Boolean = "boolean",
    ArrayOfObject = "array-of-object"
}

/**Conains validation attributes of the input control */
export enum ValidationAttribute {
    Required = "required",
    Condition = "condition"
}

/**Enum for  DQA type */
export enum DQAType {
    DeskLevel = "DL",
    ServiceLevel = "SL"
}

/// <summary>
/// Represents respondent type of Question Bank
/// </summary>
export enum RespondentType {
    SubnationalLevel = 1,
    ServiceDeliveryLevel = 2,
    CommunityLevel = 3
}

/** Enum for Question id */
export enum QuestionIds {
    Que_2_2_1_1 = "4BF0B217-9124-47DF-9385-2EC1DD63D652",
    Que_3_3_1_1 = "C86F918D-ADC6-4DAA-99F5-4AB253A34510",
    Que_3_4_1_1 = "E4769D5D-ED62-4BA2-8903-9828D682BB7C",
    Que_3_5_1_3 = "5AD32810-80A9-4602-825F-81C976DEDB25",
    Que_3_2_1_1 = "61C11183-7F06-497E-ADC5-9080B6BC91AA",
}

/** Enum to represent the categories of desk review variables */
export enum DRVariableCaseCategory {
    PVivax = 1,
    MalariaInpatient = 2
}

/**Enum to show status color on the world map based on the assessments*/
export enum WorldMapAssessmentStatusCountries {
    DefaultStatus = 0,
    InprogessAssessment = AssessmentStatus.InProgress,
    CompletedOneAssessment = AssessmentStatus.Published,
    //We only have published and inprogress status, so adding 1 to show different color on the world map
    MoreThanOneAssessment = AssessmentStatus.Published + 1,
}

/**Enum to show status color on the world map based on the assessments approach*/
export enum WorldMapAssessmentApproachCountries {
    RapidAssessment = 1,
    TailoredAssessment = 2,
    ComprehensiveAssessment = 3,
}


/** Enum for analytical output indicators view screen type*/
export enum AnalyticalOutputType {
    Table = 1,
    TabWithTable = 2,
    Graph = 3,
    TabWithGraph = 4,
    MultiLineGraph = 5,
    MultipleTable = 6,
    Diagram = 7,
    InteractiveTable = 8
}

/** Enum for analytical output chart type*/
export enum GraphType {
    Line = 1, // Line graph chart
    Bar = 2   // Bar graph chart
}

/**Enum for case strategy type for selection */
export enum CaseStrategySelectionType {
    BurdenReduction = 1,
    Elimination = 2,
    Both = 3
}

/** Enum for DL DQA analytical output indicators view screen type*/
export enum DLDQAReportType {
    TabWithTableChart = 1,
    MultipleTabsWithTableChart = 2,
    SidebarTabsWithTableChart = 3
}

/** Enum for geo graphic level */
export enum GeographicLevels {
    National = 1,
    Regional = 2,
    District = 3
}

/** Enum for shell table calculation result types */
export enum ShellTableQuestionCalculationResultTypes {
    Percentage = 1,
    Average = 2
}

/** Enum for health facility type */
export enum HealthFacilityType {
    Public = 1,
    Private = 2,
    Community = 3
}

/** Enum for national summary result type */
export enum DQADLSummaryResultType {
    NationalLevelResults = 1,
    NationalLevelTarget = 2
}

/** Enum for dqa variable applicable for */
export enum DQAVariableApplicableFor {
    ServiceLevel = 1,
    DeskLevel = 2,
    Both = 3
}

/** Enum for met not met status of indicator*/
export enum MetNotMetStatus {
    Met = 2,
    PartiallyMet = 1,
    NotMet = 0,
    NotAssessed = 3
}

/// <summary>
/// Dashboard steppers enum
/// </summary>
export enum DashboardStepper {
    Global = 0,
    Country = 1
}

/** Enum for Caclulation Question id */
export enum QuestionIds_For_Caclulation {
    Que_1_3_3 = "355938ca-d48a-419e-9d80-15510c71b473",
    Que_1_3_4 = "365938ca-d48a-419e-9d80-15510c71b474",
    Que_2_4_1 = "535938ca-d48a-419e-9d80-15510c71b491",
    Que_4_3_2 = "885938ca-d48a-419e-9d80-15510c71b526",
    Que_4_3_3 = "895938ca-d48a-419e-9d80-15510c71b527",
    Que_3_5_2 = "765938ca-d48a-419e-9d80-15510c71b514"
}

/** Enum for Option Order */
export enum OptionOrders {
    OptionOrder_1 = 0,
    OptionOrder_2 = 1,
    OptionOrder_3 = 2,
    OptionOrder_4 = 3
}


export enum Language {
    en = "en",
    fr = "fr"
}