﻿using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.Shared
{
    /// <summary>
    /// Rule to check if the passed date range is a valid date range or not
    /// </summary>
    public class DateRangeShouldBeValidRule : IBusinessRule
    {
        private readonly DateTime _startDate;
        private readonly DateTime _endDate;
        private readonly ITranslationService _translationService ;

        public DateRangeShouldBeValidRule(ITranslationService translationService, DateTime startDate, DateTime endDate)
        {
            _startDate = startDate;
            _endDate = endDate;
            _translationService = translationService;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.InvalidDateRange);

        public bool IsBroken()
        {
            // Debug logging for date comparison
            System.Diagnostics.Debug.WriteLine($"DateRangeShouldBeValidRule - StartDate: {_startDate:yyyy-MM-dd HH:mm:ss}, EndDate: {_endDate:yyyy-MM-dd HH:mm:ss}");
            System.Diagnostics.Debug.WriteLine($"DateRangeShouldBeValidRule - StartDate >= EndDate: {_startDate >= _endDate}");
            System.Diagnostics.Debug.WriteLine($"DateRangeShouldBeValidRule - StartDate.Kind: {_startDate.Kind}, EndDate.Kind: {_endDate.Kind}");

            return _startDate >= _endDate;
        }
    }
}
